<template>
  <div class="field-display-config-manage">
    <a-card>
      <template #title>
        <Icon icon="ant-design:setting-outlined" />
        折叠区域字段配置管理
      </template>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <Icon icon="ant-design:plus-outlined" />
            新建配置
          </a-button>
          <a-button @click="handleRefresh">
            <Icon icon="ant-design:reload-outlined" />
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="配置名称">
            <a-input v-model:value="searchForm.configName" placeholder="请输入配置名称" allow-clear />
          </a-form-item>
          <a-form-item label="表单类型">
            <a-select v-model:value="searchForm.formType" placeholder="请选择表单类型" allow-clear style="width: 150px">
              <a-select-option value="customer_reg">客户登记表单</a-select-option>
              <a-select-option value="company_reg">单位登记表单</a-select-option>
              <a-select-option value="exam_config">体检配置表单</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.isActive" placeholder="请选择状态" allow-clear style="width: 120px">
              <a-select-option :value="true">启用</a-select-option>
              <a-select-option :value="false">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <Icon icon="ant-design:search-outlined" />
                搜索
              </a-button>
              <a-button @click="handleReset">
                <Icon icon="ant-design:clear-outlined" />
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'formType'">
            <a-tag :color="getFormTypeColor(record.formType)">
              {{ getFormTypeName(record.formType) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'isActive'">
            <a-switch
              v-model:checked="record.isActive"
              @change="handleStatusChange(record)"
              :loading="record.statusLoading"
            />
          </template>
          
          <template v-else-if="column.key === 'fieldCount'">
            <a-badge :count="getVisibleFieldCount(record)" :number-style="{ backgroundColor: '#52c41a' }" />
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                <Icon icon="ant-design:edit-outlined" />
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleCopy(record)">
                <Icon icon="ant-design:copy-outlined" />
                复制
              </a-button>
              <a-popconfirm
                title="确定要删除这个配置吗？"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  <Icon icon="ant-design:delete-outlined" />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 配置编辑弹窗 -->
    <FormFieldConfigModal
      ref="configModalRef"
      @success="handleRefresh"
    />

    <!-- 复制配置弹窗 -->
    <a-modal
      v-model:open="copyModalVisible"
      title="复制配置"
      @ok="handleCopyConfirm"
      @cancel="copyModalVisible = false"
    >
      <a-form :model="copyForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="配置名称" name="configName" :rules="[{ required: true, message: '请输入配置名称' }]">
          <a-input v-model:value="copyForm.configName" placeholder="请输入新配置名称" />
        </a-form-item>
        <a-form-item label="目标体检中心" name="targetCenterId">
          <a-select v-model:value="copyForm.targetCenterId" placeholder="请选择目标体检中心">
            <!-- 这里应该动态加载体检中心列表 -->
            <a-select-option value="center1">体检中心1</a-select-option>
            <a-select-option value="center2">体检中心2</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '@/components/Icon';
import FormFieldConfigModal from './components/FormFieldConfigModal.vue';
import {
  getFormDisplayConfigList,
  deleteFormDisplayConfig,
  saveFormDisplayConfig,
} from './FormFieldConfig.api';
import { FormDisplayConfig } from './config/FieldDefinitions';

// 搜索表单
const searchForm = reactive({
  configName: '',
  formType: undefined,
  isActive: undefined,
});

// 表格数据
const dataSource = ref<FormDisplayConfig[]>([]);
const loading = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
});

// 弹窗引用
const configModalRef = ref();
const copyModalVisible = ref(false);
const copyForm = reactive({
  sourceId: '',
  configName: '',
  targetCenterId: '',
});

// 表格列定义
const columns = [
  {
    title: '配置名称',
    dataIndex: 'configName',
    key: 'configName',
    width: 200,
  },
  {
    title: '表单类型',
    dataIndex: 'formType',
    key: 'formType',
    width: 120,
  },
  {
    title: '体检中心',
    dataIndex: 'centerName',
    key: 'centerName',
    width: 150,
  },
  {
    title: '显示字段数',
    key: 'fieldCount',
    width: 100,
  },
  {
    title: '状态',
    key: 'isActive',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
  },
];

// 获取表单类型颜色
const getFormTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    customer_reg: 'blue',
    company_reg: 'green',
    exam_config: 'orange',
  };
  return colorMap[type] || 'default';
};

// 获取表单类型名称
const getFormTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    customer_reg: '客户登记表单',
    company_reg: '单位登记表单',
    exam_config: '体检配置表单',
  };
  return nameMap[type] || type;
};

// 获取可见字段数量
const getVisibleFieldCount = (record: FormDisplayConfig) => {
  return record.fields?.filter(field => field.isVisible).length || 0;
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchForm,
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
    };
    
    const result = await getFormDisplayConfigList(params);
    dataSource.value = result.records;
    pagination.total = result.total;
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    configName: '',
    formType: undefined,
    isActive: undefined,
  });
  handleSearch();
};

// 刷新
const handleRefresh = () => {
  loadData();
};

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadData();
};

// 新增
const handleAdd = () => {
  configModalRef.value?.open();
};

// 编辑
const handleEdit = (record: FormDisplayConfig) => {
  configModalRef.value?.open(record);
};

// 复制
const handleCopy = (record: FormDisplayConfig) => {
  copyForm.sourceId = record.id!;
  copyForm.configName = `${record.configName}_副本`;
  copyForm.targetCenterId = record.centerId;
  copyModalVisible.value = true;
};

// 确认复制
const handleCopyConfirm = async () => {
  try {
    const sourceConfig = dataSource.value.find(item => item.id === copyForm.sourceId);
    if (sourceConfig) {
      const newConfig = {
        ...sourceConfig,
        id: undefined,
        configName: copyForm.configName,
        centerId: copyForm.targetCenterId,
      };
      await saveFormDisplayConfig(newConfig);
      message.success('复制成功');
      copyModalVisible.value = false;
      loadData();
    }
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败');
  }
};

// 删除
const handleDelete = async (record: FormDisplayConfig) => {
  try {
    await deleteFormDisplayConfig(record.id!);
    message.success('删除成功');
    loadData();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 状态变更
const handleStatusChange = async (record: FormDisplayConfig) => {
  try {
    record.statusLoading = true;
    await saveFormDisplayConfig(record);
    message.success('状态更新成功');
  } catch (error) {
    console.error('状态更新失败:', error);
    message.error('状态更新失败');
    // 回滚状态
    record.isActive = !record.isActive;
  } finally {
    record.statusLoading = false;
  }
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.field-display-config-manage {
  padding: 16px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
