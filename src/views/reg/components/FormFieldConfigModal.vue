<template>
  <a-modal v-model:open="visible" title="表单字段显示配置" width="1000px" :confirm-loading="confirmLoading" @ok="handleSave" @cancel="handleCancel">
    <a-spin :spinning="loading">
      <div class="config-container">
        <!-- 配置基本信息 -->
        <a-card size="small" title="配置信息" class="config-info-card">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="表单类型" v-bind="validateInfos.formType">
                <a-select v-model:value="formData.formType" placeholder="请选择表单类型" @change="handleFormTypeChange">
                  <a-select-option value="customer_reg">客户登记表单</a-select-option>
                  <a-select-option value="company_reg">单位登记表单</a-select-option>
                  <a-select-option value="exam_config">体检配置表单</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否启用">
                <a-switch v-model:checked="formData.isActive" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 字段显示配置区域 -->
        <a-card size="small" title="字段显示位置配置" class="fields-config-card">
          <template #extra>
            <a-space>
              <a-button size="small" @click="loadDefaultConfig">
                <Icon icon="ant-design:reload-outlined" /> 加载默认配置
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleBatchOperation">
                    <a-menu-item key="all-outside">全部设为外部显示</a-menu-item>
                    <a-menu-item key="all-collapse">全部设为折叠面板内</a-menu-item>
                    <a-menu-item key="all-hidden">全部隐藏</a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  <Icon icon="ant-design:setting-outlined" /> 批量操作
                  <Icon icon="ant-design:down-outlined" />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>

          <!-- 显示位置说明 -->
          <div class="location-legend">
            <a-space>
              <a-tag color="green">
                <Icon icon="ant-design:eye-outlined" /> 外部显示：在折叠面板外直接显示
              </a-tag>
              <a-tag color="blue">
                <Icon icon="ant-design:folder-outlined" /> 折叠面板内：在可折叠区域内显示
              </a-tag>
              <a-tag color="red">
                <Icon icon="ant-design:eye-invisible-outlined" /> 隐藏：不显示该字段
              </a-tag>
            </a-space>
          </div>

          <!-- 字段列表 -->
          <div class="fields-list">
            <a-table
              :columns="fieldColumns"
              :data-source="formData.fields"
              :pagination="false"
              size="small"
              row-key="fieldKey"
              class="fields-table"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fieldName'">
                  <div class="field-name-cell">
                    <span class="field-name">{{ record.fieldName }}</span>
                    <a-tag v-if="record.groupName" size="small" color="blue">
                      {{ getGroupDisplayName(record.groupName) }}
                    </a-tag>
                  </div>
                </template>
                <template v-else-if="column.key === 'displayLocation'">
                  <a-select
                    v-model:value="record.displayLocation"
                    size="small"
                    style="width: 140px"
                    @change="handleLocationChange(record)"
                  >
                    <a-select-option value="outside">
                      <Icon icon="ant-design:eye-outlined" style="color: #52c41a" /> 外部显示
                    </a-select-option>
                    <a-select-option value="collapse">
                      <Icon icon="ant-design:folder-outlined" style="color: #1890ff" /> 折叠面板内
                    </a-select-option>
                    <a-select-option value="hidden">
                      <Icon icon="ant-design:eye-invisible-outlined" style="color: #ff4d4f" /> 隐藏
                    </a-select-option>
                  </a-select>
                </template>
                <template v-else-if="column.key === 'sortOrder'">
                  <a-input-number
                    v-model:value="record.sortOrder"
                    size="small"
                    :min="1"
                    :max="999"
                    style="width: 80px"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </a-card>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import { Form, message } from 'ant-design-vue';
  import { Icon } from '@/components/Icon';
  import {
    saveFormDisplayConfig,
  } from '../FormFieldConfig.api';
  import {
    FormDisplayConfig,
    FieldDisplayConfig,
    FieldDisplayLocation,
    FieldGroups,
  } from '../config/FieldDefinitions';
  import { FormTypes, customerRegFieldManager } from '../config/FieldConfigUtils';

  const emit = defineEmits(['register', 'success']);

  const visible = ref(false);
  const loading = ref(false);
  const confirmLoading = ref(false);

  const formData = reactive<FormDisplayConfig>({
    configName: '',
    centerId: '',
    centerName: '',
    formType: 'customer_reg',
    isActive: true,
    fields: [],
  });

  // 表单验证
  const useForm = Form.useForm;
  const rules = reactive({
    formType: [{ required: true, message: '请选择表单类型' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, rules);

  // 表格列定义
  const fieldColumns = [
    {
      title: '字段名称',
      key: 'fieldName',
      width: 200,
    },
    {
      title: '显示位置',
      key: 'displayLocation',
      width: 160,
    },
    {
      title: '排序',
      key: 'sortOrder',
      width: 100,
    },
  ];

  // 获取分组显示名称
  const getGroupDisplayName = (groupName: string) => {
    const groupMap = {
      [FieldGroups.BASIC_INFO]: '基础信息',
      [FieldGroups.ADDRESS_INFO]: '地址信息',
      [FieldGroups.CONTACT_INFO]: '联系信息',
      [FieldGroups.HEALTH_INFO]: '健康信息',
      [FieldGroups.CERTIFICATE_INFO]: '证件信息',
      [FieldGroups.WORK_INFO]: '工作信息',
      [FieldGroups.EXAM_INFO]: '体检信息',
      [FieldGroups.OTHER_INFO]: '其他信息',
    };
    return groupMap[groupName] || groupName;
  };

  // 表单类型变化处理
  const handleFormTypeChange = (formType: string) => {
    loadDefaultConfig();
  };

  // 批量操作处理
  const handleBatchOperation = ({ key }: { key: string }) => {
    switch (key) {
      case 'all-outside':
        formData.fields.forEach((field) => {
          field.displayLocation = FieldDisplayLocation.OUTSIDE;
        });
        message.success('已将所有字段设为外部显示');
        break;
      case 'all-collapse':
        formData.fields.forEach((field) => {
          field.displayLocation = FieldDisplayLocation.COLLAPSE;
        });
        message.success('已将所有字段设为折叠面板内显示');
        break;
      case 'all-hidden':
        formData.fields.forEach((field) => {
          field.displayLocation = FieldDisplayLocation.HIDDEN;
        });
        message.success('已将所有字段设为隐藏');
        break;
    }
  };

  // 显示位置变化处理
  const handleLocationChange = (field: FieldDisplayConfig) => {
    // 可以在这里添加特殊逻辑，比如某些字段不允许隐藏等
    console.log(`字段 ${field.fieldName} 显示位置变更为: ${field.displayLocation}`);
  };

  // 加载默认配置
  const loadDefaultConfig = () => {
    if (formData.formType === FORM_TYPES.CUSTOMER_REG) {
      const defaultFields = JSON.parse(JSON.stringify(CUSTOMER_REG_CONFIGURABLE_FIELDS));
      // 确保每个字段都有displayLocation属性，如果没有则根据isVisible转换
      formData.fields = defaultFields.map((field, index) => ({
        ...field,
        displayLocation: field.displayLocation || (field.isVisible ? FieldDisplayLocation.COLLAPSE : FieldDisplayLocation.HIDDEN),
        sortOrder: field.sortOrder || (index + 1) * 10,
      }));
    } else {
      formData.fields = [];
    }
    message.success('已加载默认配置');
  };

  // 保存配置
  const handleSave = async () => {
    try {
      await validate();
      confirmLoading.value = true;

      // 设置体检中心信息（实际应该从登录用户信息获取）
      if (!formData.centerId) {
        formData.centerId = 'current_center_id';
        formData.centerName = '当前体检中心';
      }

      // 准备保存的数据，确保字段配置格式正确
      const saveData = {
        ...formData,
        fields: formData.fields.map(field => ({
          ...field,
          // 确保同时设置新旧字段以保持兼容性
          displayLocation: field.displayLocation,
          isVisible: field.displayLocation === FieldDisplayLocation.COLLAPSE || field.displayLocation === FieldDisplayLocation.OUTSIDE,
        }))
      };

      await saveFormDisplayConfig(saveData);
      message.success('保存成功');
      emit('success');
      handleCancel();
    } catch (error) {
      console.error('保存失败:', error);

      // 如果是唯一约束冲突错误
      if (error?.message?.includes('Duplicate entry') || error?.message?.includes('uk_center_form')) {
        message.error('该表单类型的配置已存在，请刷新页面后重试或联系管理员');
      }
      // 如果是 404 错误，说明后端接口还没有实现
      else if (error?.response?.status === 404) {
        message.warning('后端接口尚未实现，配置暂时无法保存到服务器');
        // 可以选择将配置保存到本地存储作为临时方案
        const localData = {
          ...formData,
          fields: formData.fields.map(field => ({
            ...field,
            displayLocation: field.displayLocation,
            isVisible: field.displayLocation === FieldDisplayLocation.COLLAPSE || field.displayLocation === FieldDisplayLocation.OUTSIDE,
          }))
        };
        localStorage.setItem('fieldDisplayConfig', JSON.stringify(localData));
        emit('success');
        handleCancel();
      } else {
        message.error('保存失败：' + (error?.message || '请稍后重试'));
      }
    } finally {
      confirmLoading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    visible.value = false;
    resetFields();
    formData.fields = [];
  };

  // 打开弹窗
  const open = (config?: FormDisplayConfig) => {
    visible.value = true;
    if (config) {
      // 复制基本配置信息
      formData.id = config.id;
      formData.configName = config.configName;
      formData.centerId = config.centerId;
      formData.centerName = config.centerName;
      formData.formType = config.formType;
      formData.isActive = config.isActive;

      // 合并字段配置：以完整字段列表为基础，更新显示状态
      const fullFieldList = JSON.parse(JSON.stringify(CUSTOMER_REG_CONFIGURABLE_FIELDS));
      if (config.fields && config.fields.length > 0) {
        // 根据传入的配置更新字段的显示状态和位置
        fullFieldList.forEach((field, index) => {
          const configField = config.fields.find(cf => cf.fieldKey === field.fieldKey);
          if (configField) {
            // 优先使用新的displayLocation，如果没有则根据isVisible转换
            field.displayLocation = configField.displayLocation ||
              (configField.isVisible ? FieldDisplayLocation.COLLAPSE : FieldDisplayLocation.HIDDEN);
            field.sortOrder = configField.sortOrder || (index + 1) * 10;
          } else {
            // 如果配置中没有该字段，设置默认值
            field.displayLocation = field.displayLocation || FieldDisplayLocation.HIDDEN;
            field.sortOrder = field.sortOrder || (index + 1) * 10;
          }
        });
      } else {
        // 如果没有字段配置，设置默认值
        fullFieldList.forEach((field, index) => {
          field.displayLocation = field.displayLocation || FieldDisplayLocation.HIDDEN;
          field.sortOrder = field.sortOrder || (index + 1) * 10;
        });
      }
      formData.fields = fullFieldList;
    } else {
      // 新建时加载默认配置
      loadDefaultConfig();
    }
  };

  defineExpose({
    open,
  });
</script>

<style scoped>
  .config-container {
    max-height: 700px;
    overflow-y: auto;
  }

  .config-info-card {
    margin-bottom: 16px;
  }

  .fields-config-card {
    margin-bottom: 16px;
  }

  .location-legend {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
  }

  .fields-list {
    max-height: 500px;
    overflow-y: auto;
  }

  .fields-table {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
  }

  .field-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .field-name {
    font-weight: 500;
    color: #333;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #f5f5f5;
  }

  :deep(.ant-select-dropdown) {
    z-index: 9999;
  }
</style>
