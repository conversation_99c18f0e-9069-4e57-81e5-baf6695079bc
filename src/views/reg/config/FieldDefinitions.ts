/**
 * 字段定义配置
 * 统一管理所有可配置字段的定义和元数据
 */

/**
 * 字段键枚举 - 确保类型安全
 */
export enum FieldKeys {
  // 基础信息
  NATION = 'nation',
  BLOOD_TYPE = 'bloodType',
  COUNTRY_CODE = 'countryCode',
  POST_CODE = 'postCode',
  EDU_LEVEL = 'eduLevel',
  MARRIAGE_STATUS = 'marriageStatus',
  CUSTOMER_CATEGORY = 'customerCategory',
  
  // 地址信息
  PCA_CODE = 'pcaCode',
  ADDRESS = 'address',
  
  // 联系信息
  EMERGENCY_CONTACT = 'emergencyContact',
  EMERGENCY_PHONE = 'emergencyPhone',
  
  // 健康信息
  IS_PREGNANCY_PREP = 'isPregnancyPrep',
  PREGNANCY_FLAG = 'pregnancyFlag',
  
  // 证件信息
  HEALTH_NO = 'healthNo',
  EXAM_CARD_NO = 'examCardNo',
  ORIGINAL_ID_CARD = 'originalIdCard',
  ORIGIN_CUSTOMER_RELATION = 'originCustomerRelation',
  
  // 职业信息
  WORK_YEARS = 'workYears',
  COMPANY_NAME = 'companyName',
  COMPANY_ID = 'companyId',
  COMPANY_DEPT_ID = 'companyDeptId',
  BELONG_COMPANY = 'belongCompany',
  DEPARTMENT = 'department',
  
  // 体检信息
  SUPPLY_FLAG = 'supplyFlag',
  PRE_PAY_FLAG = 'prePayFlag',
  RE_EXAM_STATUS = 'reExamStatus',
  RE_EXAM_REMARK = 'reExamRemark',
  
  // 其他信息
  RECIPE_TITLE = 'recipeTitle',
  INTRODUCER = 'introducer',
  SECRET_LEVEL = 'secretLevel',
  REMARK = 'remark',
}

/**
 * 字段显示位置枚举
 */
export enum FieldDisplayLocation {
  OUTSIDE = 'outside',     // 在折叠面板外显示
  COLLAPSE = 'collapse',   // 在折叠面板内显示
  HIDDEN = 'hidden',       // 隐藏不显示
}

/**
 * 字段分组枚举
 */
export enum FieldGroups {
  BASIC_INFO = 'basicInfo',
  ADDRESS_INFO = 'addressInfo', 
  CONTACT_INFO = 'contactInfo',
  HEALTH_INFO = 'healthInfo',
  CERTIFICATE_INFO = 'certificateInfo',
  WORK_INFO = 'workInfo',
  EXAM_INFO = 'examInfo',
  OTHER_INFO = 'otherInfo',
}

/**
 * 字段类型枚举
 */
export enum FieldTypes {
  INPUT = 'input',
  SELECT = 'select',
  DICT_SELECT = 'dictSelect',
  ASYNC_SELECT = 'asyncSelect',
  TEXTAREA = 'textarea',
  SWITCH = 'switch',
  NUMBER = 'number',
  CASCADER = 'cascader',
  SEARCH = 'search',
}

/**
 * 字段元数据接口
 */
export interface FieldMetadata {
  key: FieldKeys;
  name: string;
  type: FieldTypes;
  group: FieldGroups;
  defaultLocation: FieldDisplayLocation;
  span?: number;
  placeholder?: string;
  dictCode?: string;
  dict?: string;
  options?: Array<{ label: string; value: any }>;
  required?: boolean;
  disabled?: boolean;
  extra?: string;
  rules?: any[];
}

/**
 * 字段显示配置接口
 */
export interface FieldDisplayConfig {
  id?: string;                    // 主键ID
  configId?: string;              // 配置ID
  fieldKey: FieldKeys;            // 字段标识
  fieldName: string;              // 字段名称
  displayLocation: FieldDisplayLocation; // 显示位置
  groupName: FieldGroups;         // 分组名称
  sortOrder?: number;             // 排序号
  isRequired?: boolean;           // 是否必填
  fieldDescription?: string;      // 字段描述
  isVisible?: boolean;            // 兼容旧版本，逐步废弃
}

/**
 * 表单显示配置接口
 */
export interface FormDisplayConfig {
  id?: string;                    // 主键ID
  configName: string;             // 配置名称
  centerId: string;               // 体检中心ID
  centerName: string;             // 体检中心名称
  formType: string;               // 表单类型
  isActive: boolean;              // 是否启用
  fields: FieldDisplayConfig[];   // 字段配置列表
  createBy?: string;              // 创建人
  createTime?: string;            // 创建时间
  updateBy?: string;              // 更新人
  updateTime?: string;            // 更新时间
  delFlag?: boolean;              // 删除标志
}

/**
 * 客户登记表单字段元数据定义
 */
export const CUSTOMER_REG_FIELD_METADATA: Record<FieldKeys, FieldMetadata> = {
  [FieldKeys.NATION]: {
    key: FieldKeys.NATION,
    name: '民族',
    type: FieldTypes.INPUT,
    group: FieldGroups.BASIC_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请输入民族',
  },
  
  [FieldKeys.BLOOD_TYPE]: {
    key: FieldKeys.BLOOD_TYPE,
    name: '血型',
    type: FieldTypes.DICT_SELECT,
    group: FieldGroups.BASIC_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请选择血型',
    dictCode: 'blood_type',
  },
  
  [FieldKeys.COUNTRY_CODE]: {
    key: FieldKeys.COUNTRY_CODE,
    name: '国籍',
    type: FieldTypes.ASYNC_SELECT,
    group: FieldGroups.BASIC_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请选择国籍',
    dict: 'country',
  },
  
  [FieldKeys.POST_CODE]: {
    key: FieldKeys.POST_CODE,
    name: '邮政编码',
    type: FieldTypes.INPUT,
    group: FieldGroups.ADDRESS_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请输入邮政编码',
  },
  
  [FieldKeys.EDU_LEVEL]: {
    key: FieldKeys.EDU_LEVEL,
    name: '文化程度',
    type: FieldTypes.DICT_SELECT,
    group: FieldGroups.BASIC_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请选择文化程度',
    dictCode: 'edu_level',
  },
  
  [FieldKeys.MARRIAGE_STATUS]: {
    key: FieldKeys.MARRIAGE_STATUS,
    name: '婚姻状况',
    type: FieldTypes.DICT_SELECT,
    group: FieldGroups.BASIC_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请选择婚姻状况',
    dictCode: 'marriage_status',
  },
  
  [FieldKeys.CUSTOMER_CATEGORY]: {
    key: FieldKeys.CUSTOMER_CATEGORY,
    name: '客户类别',
    type: FieldTypes.DICT_SELECT,
    group: FieldGroups.BASIC_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请选择客户类别',
    dictCode: 'customer_category',
  },
  
  [FieldKeys.PCA_CODE]: {
    key: FieldKeys.PCA_CODE,
    name: '省市区县',
    type: FieldTypes.CASCADER,
    group: FieldGroups.ADDRESS_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 24,
    placeholder: '请选择省市区县',
  },
  
  [FieldKeys.ADDRESS]: {
    key: FieldKeys.ADDRESS,
    name: '详细地址',
    type: FieldTypes.TEXTAREA,
    group: FieldGroups.ADDRESS_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请输入详细地址',
  },
  
  [FieldKeys.EMERGENCY_CONTACT]: {
    key: FieldKeys.EMERGENCY_CONTACT,
    name: '紧急联系人',
    type: FieldTypes.INPUT,
    group: FieldGroups.CONTACT_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请输入紧急联系人',
  },
  
  [FieldKeys.EMERGENCY_PHONE]: {
    key: FieldKeys.EMERGENCY_PHONE,
    name: '紧急电话',
    type: FieldTypes.INPUT,
    group: FieldGroups.CONTACT_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请输入紧急电话',
  },
  
  [FieldKeys.IS_PREGNANCY_PREP]: {
    key: FieldKeys.IS_PREGNANCY_PREP,
    name: '是否备孕',
    type: FieldTypes.SWITCH,
    group: FieldGroups.HEALTH_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
  },
  
  [FieldKeys.PREGNANCY_FLAG]: {
    key: FieldKeys.PREGNANCY_FLAG,
    name: '是否备孕',
    type: FieldTypes.SWITCH,
    group: FieldGroups.HEALTH_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
  },
  
  [FieldKeys.HEALTH_NO]: {
    key: FieldKeys.HEALTH_NO,
    name: '健康证号',
    type: FieldTypes.INPUT,
    group: FieldGroups.CERTIFICATE_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入健康证号',
  },
  
  [FieldKeys.EXAM_CARD_NO]: {
    key: FieldKeys.EXAM_CARD_NO,
    name: '体检卡号',
    type: FieldTypes.INPUT,
    group: FieldGroups.CERTIFICATE_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入体检卡号',
  },
  
  [FieldKeys.WORK_YEARS]: {
    key: FieldKeys.WORK_YEARS,
    name: '工龄',
    type: FieldTypes.NUMBER,
    group: FieldGroups.WORK_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入工龄',
  },
  
  [FieldKeys.COMPANY_NAME]: {
    key: FieldKeys.COMPANY_NAME,
    name: '单位名称',
    type: FieldTypes.INPUT,
    group: FieldGroups.WORK_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入单位名称',
  },
  
  [FieldKeys.COMPANY_ID]: {
    key: FieldKeys.COMPANY_ID,
    name: '所属单位',
    type: FieldTypes.ASYNC_SELECT,
    group: FieldGroups.WORK_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    dict: 'company where pid=0 and del_flag=0,name,id',
  },
  
  [FieldKeys.COMPANY_DEPT_ID]: {
    key: FieldKeys.COMPANY_DEPT_ID,
    name: '所属部门',
    type: FieldTypes.SELECT,
    group: FieldGroups.WORK_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '所属部门',
  },
  
  [FieldKeys.BELONG_COMPANY]: {
    key: FieldKeys.BELONG_COMPANY,
    name: '所属单位',
    type: FieldTypes.INPUT,
    group: FieldGroups.WORK_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入所属单位',
  },
  
  [FieldKeys.DEPARTMENT]: {
    key: FieldKeys.DEPARTMENT,
    name: '所属科室',
    type: FieldTypes.INPUT,
    group: FieldGroups.WORK_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入所属科室',
  },
  
  [FieldKeys.SUPPLY_FLAG]: {
    key: FieldKeys.SUPPLY_FLAG,
    name: '补检',
    type: FieldTypes.SWITCH,
    group: FieldGroups.EXAM_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
  },
  
  [FieldKeys.PRE_PAY_FLAG]: {
    key: FieldKeys.PRE_PAY_FLAG,
    name: '预缴',
    type: FieldTypes.SWITCH,
    group: FieldGroups.EXAM_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
  },
  
  [FieldKeys.RE_EXAM_STATUS]: {
    key: FieldKeys.RE_EXAM_STATUS,
    name: '是否复查',
    type: FieldTypes.SWITCH,
    group: FieldGroups.EXAM_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
  },
  
  [FieldKeys.RE_EXAM_REMARK]: {
    key: FieldKeys.RE_EXAM_REMARK,
    name: '复查备注',
    type: FieldTypes.INPUT,
    group: FieldGroups.EXAM_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请输入复查备注',
  },
  
  [FieldKeys.ORIGINAL_ID_CARD]: {
    key: FieldKeys.ORIGINAL_ID_CARD,
    name: '原检证件号',
    type: FieldTypes.SEARCH,
    group: FieldGroups.CERTIFICATE_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请输入原体检人身份证号',
  },
  
  [FieldKeys.ORIGIN_CUSTOMER_RELATION]: {
    key: FieldKeys.ORIGIN_CUSTOMER_RELATION,
    name: '与原检关系',
    type: FieldTypes.DICT_SELECT,
    group: FieldGroups.CERTIFICATE_INFO,
    defaultLocation: FieldDisplayLocation.OUTSIDE,
    span: 12,
    placeholder: '请选择与原检关系',
    dictCode: 'origin_relation',
  },
  
  [FieldKeys.RECIPE_TITLE]: {
    key: FieldKeys.RECIPE_TITLE,
    name: '发票抬头',
    type: FieldTypes.INPUT,
    group: FieldGroups.OTHER_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入发票抬头',
  },
  
  [FieldKeys.INTRODUCER]: {
    key: FieldKeys.INTRODUCER,
    name: '介绍人',
    type: FieldTypes.INPUT,
    group: FieldGroups.OTHER_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入介绍人',
  },
  
  [FieldKeys.SECRET_LEVEL]: {
    key: FieldKeys.SECRET_LEVEL,
    name: '保密等级',
    type: FieldTypes.DICT_SELECT,
    group: FieldGroups.OTHER_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请选择保密等级',
    dictCode: 'secret_level',
  },
  
  [FieldKeys.REMARK]: {
    key: FieldKeys.REMARK,
    name: '备注',
    type: FieldTypes.TEXTAREA,
    group: FieldGroups.OTHER_INFO,
    defaultLocation: FieldDisplayLocation.COLLAPSE,
    span: 12,
    placeholder: '请输入备注',
  },
};
